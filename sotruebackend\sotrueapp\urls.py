from .application.controllers import SotrueAppUserController
from .application.controllers import SotrueAppAdminController
from .application.controllers import SotrueAppPlayController
from django.urls import path
from django.views.decorators.csrf import csrf_exempt


urlpatterns = [
    path('appservice', csrf_exempt(SotrueAppUserController.SotrueAppUserController.as_view()), name='appservice'),
    path('adminservice', csrf_exempt(SotrueAppAdminController.SotrueAppAdminController.as_view()), name='adminservice'),
    path('playlist', csrf_exempt(SotrueAppPlayController.SotrueAppPlayController.as_view()), name='playlist')
]