from .AppUtils import AppUtils

class AppAccessControl:

    def __init__(self,action_override,session,audits,connDB):
        self.action_override = action_override
        self.session = session
        self.audits = audits
        self.connDB = connDB
        self.error = None
        self.utils = AppUtils


    def is_access_allowed(self,action_code,_access_key):
        if action_code == "GET_GENRE_TOPIC_MASTERS":
            return True

        if action_code not in self.action_override:
            if not self.session.is_access_key_valid(_access_key):
                self.error = "ACCESS"
                return False
            
            if self.session.is_session_valid():
                # validate the action code
                return self.get_user_access(action_code)
            else:
                self.error = "SESSION"
                return False
        else:
            return True

    def get_user_access(self,action_code):
        audit_rule = self.audits.get_audit_values(action_code)
        if audit_rule:
            if audit_rule["audit"]:
                qparams = [audit_rule["function"],self.session.get_session_value("_user_seq")]
                result_set = None
                if not self.session.get_session_value("_admin_user"):
                    result_set = self.connDB.execute_prepared_stmt("sotruegeneric","GET_FUNCTION_ACCESS",qparams)
                else:
                    result_set = self.connDB.execute_prepared_stmt("sotruegeneric","GET_FUNCTION_ACCESS_ADMIN",qparams)
                    
                if len(result_set):
                    return True
                else:
                    qparams = [audit_rule["function"]]
                    result_set = self.connDB.execute_prepared_stmt("sotruegeneric","GET_FUNCTION_TYPE",qparams)
                    if(result_set[0].function_type == "DEFAULT"):
                        return True
                    
                    self.error = "ACCESS"
                    return False
            else:
                return True
        else:
            self.error = "ACCESS"
            return False

    
    def get_error(self):
        return self.error
#
# Testing code
#
if __name__ == "__main__":
    pass
