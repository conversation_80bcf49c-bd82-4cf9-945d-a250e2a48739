version: "3.8"

services:
  web:
    build: .
    container_name: sotrue-backend
    volumes:
      - .:/app
      - C:/tmp/sotruelogs:/tmp/sotruelogs
    ports:
      - "8000:8000"
    env_file:
      - .env
    environment:
      - PORT=8000
      - ALLOWED_HOSTS=*
      - LOG_FILE_PATH=/tmp/sotruelogs/sotureapp.log
      - TEMP_FOLDER_PATH=/tmp/sotruelogs/
      - PYTHONUNBUFFERED=1
    depends_on:
      - db
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 2G
        reservations:
          cpus: "1"
          memory: 1G

  db:
    image: postgres:14
    container_name: sotrue-db
    volumes:
      - postgres_data:/var/lib/postgresql/data/
      - ./sotruedbdump.sql:/docker-entrypoint-initdb.d/sotruedbdump.sql
    env_file:
      - .env
    environment:
      - POSTGRES_USER=${TEST_DB_USER}
      - POSTGRES_PASSWORD=${TEST_DB_PASSWORD}
      - POSTGRES_DB=${TEST_DB_NAME}
    ports:
      - "5432:5432"
    restart: unless-stopped

volumes:
  postgres_data:
